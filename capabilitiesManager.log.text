2025-07-17 00:05:57.164 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-17 00:05:57.169 | Loaded RSA public key for plugin verification
2025-07-17 00:05:57.313 | GitHub repositories enabled in configuration
2025-07-17 00:05:57.356 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-17 00:05:57.356 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-17 00:05:57.356 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-17 00:05:57.356 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-17 00:05:57.356 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-17 00:05:57.356 | Using Consul URL: consul:8500
2025-07-17 00:05:57.434 | Successfully initialized repository of type: local
2025-07-17 00:05:57.440 | Successfully initialized repository of type: mongo
2025-07-17 00:05:57.440 | Successfully initialized repository of type: librarian-definition
2025-07-17 00:05:57.440 | Successfully initialized repository of type: git
2025-07-17 00:05:57.440 | Initializing GitHub repository with provided credentials
2025-07-17 00:05:57.440 | GitHubRepository: Initialized for cpravetz/s7plugins. Plugins dir: 'plugins'. Default branch from config/env: main
2025-07-17 00:05:57.440 | Successfully initialized repository of type: github
2025-07-17 00:05:57.440 | Refreshing plugin cache...
2025-07-17 00:05:57.440 | Loading plugins from local repository...
2025-07-17 00:05:57.440 | LocalRepo: Loading fresh plugin list
2025-07-17 00:05:57.440 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-17 00:05:57.441 | Refreshing plugin cache...
2025-07-17 00:05:57.441 | Loading plugins from local repository...
2025-07-17 00:05:57.444 | LocalRepo: Loading fresh plugin list
2025-07-17 00:05:57.444 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-17 00:05:57.458 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-17 00:05:57.493 | LocalRepo: Loading from  [
2025-07-17 00:05:57.493 |   'ACCOMPLISH',
2025-07-17 00:05:57.493 |   'API_CLIENT',
2025-07-17 00:05:57.493 |   'CHAT',
2025-07-17 00:05:57.493 |   'CODE_EXECUTOR',
2025-07-17 00:05:57.493 |   'DATA_TOOLKIT',
2025-07-17 00:05:57.493 |   'FILE_OPS_PYTHON',
2025-07-17 00:05:57.493 |   'GET_USER_INPUT',
2025-07-17 00:05:57.493 |   'SCRAPE',
2025-07-17 00:05:57.493 |   'SEARCH_PYTHON',
2025-07-17 00:05:57.493 |   'TASK_MANAGER',
2025-07-17 00:05:57.493 |   'TEXT_ANALYSIS',
2025-07-17 00:05:57.493 |   'WEATHER'
2025-07-17 00:05:57.493 | ]
2025-07-17 00:05:57.493 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:05:57.495 | LocalRepo: Loading from  [
2025-07-17 00:05:57.495 |   'ACCOMPLISH',
2025-07-17 00:05:57.495 |   'API_CLIENT',
2025-07-17 00:05:57.495 |   'CHAT',
2025-07-17 00:05:57.495 |   'CODE_EXECUTOR',
2025-07-17 00:05:57.495 |   'DATA_TOOLKIT',
2025-07-17 00:05:57.495 |   'FILE_OPS_PYTHON',
2025-07-17 00:05:57.495 |   'GET_USER_INPUT',
2025-07-17 00:05:57.495 |   'SCRAPE',
2025-07-17 00:05:57.495 |   'SEARCH_PYTHON',
2025-07-17 00:05:57.495 |   'TASK_MANAGER',
2025-07-17 00:05:57.495 |   'TEXT_ANALYSIS',
2025-07-17 00:05:57.495 |   'WEATHER'
2025-07-17 00:05:57.495 | ]
2025-07-17 00:05:57.495 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:05:57.561 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-17 00:05:57.561 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-17 00:05:57.619 | Service CapabilitiesManager registered with Consul
2025-07-17 00:05:57.624 | Successfully registered CapabilitiesManager with Consul
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-17 00:05:57.626 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-17 00:05:57.641 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-17 00:05:57.641 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-17 00:05:57.641 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-17 00:05:57.644 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-17 00:05:57.657 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-17 00:05:57.667 | Connected to RabbitMQ
2025-07-17 00:05:57.677 | LocalRepo: Locators count 12
2025-07-17 00:05:57.677 | LocalRepo: Locators count 12
2025-07-17 00:05:57.677 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:05:57.677 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:05:57.679 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-17 00:05:57.679 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-17 00:05:57.682 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-17 00:05:57.682 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-17 00:05:57.685 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-17 00:05:57.685 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-17 00:05:57.686 | Channel created successfully
2025-07-17 00:05:57.689 | RabbitMQ channel ready
2025-07-17 00:05:57.695 | CapabilitiesManager registered successfully with PostOffice
2025-07-17 00:05:57.696 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-17 00:05:57.696 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-17 00:05:57.706 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-17 00:05:57.706 | Loaded 12 plugins from local repository
2025-07-17 00:05:57.706 | Loading plugins from mongo repository...
2025-07-17 00:05:57.717 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-17 00:05:57.717 | Loaded 12 plugins from local repository
2025-07-17 00:05:57.717 | Loading plugins from mongo repository...
2025-07-17 00:05:57.794 | Connection test successful - RabbitMQ connection is stable
2025-07-17 00:05:57.794 | Creating queue: capabilitiesmanager-CapabilitiesManager
2025-07-17 00:05:57.835 | Binding queue to exchange: stage7
2025-07-17 00:05:57.871 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-17 00:05:59.089 | Loaded 0 plugins from mongo repository
2025-07-17 00:05:59.089 | Loading plugins from librarian-definition repository...
2025-07-17 00:05:59.111 | Loaded 0 plugins from mongo repository
2025-07-17 00:05:59.111 | Loading plugins from librarian-definition repository...
2025-07-17 00:05:59.133 | Loaded 0 plugins from librarian-definition repository
2025-07-17 00:05:59.133 | Loading plugins from git repository...
2025-07-17 00:05:59.140 | Loaded 0 plugins from librarian-definition repository
2025-07-17 00:05:59.140 | Loading plugins from git repository...
2025-07-17 00:05:59.200 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-17 00:05:59.200 | fatal: cannot copy '/usr/share/git-core/templates/info/exclude' to '/usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/info/exclude': File exists
2025-07-17 00:05:59.200 | 
2025-07-17 00:05:59.202 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-17 00:05:59.202 | fatal: cannot copy '/usr/share/git-core/templates/hooks/pre-commit.sample' to '/usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/hooks/pre-commit.sample': No such file or directory
2025-07-17 00:05:59.202 | 
2025-07-17 00:05:59.205 | Loaded 0 plugins from git repository
2025-07-17 00:05:59.208 | Loading plugins from github repository...
2025-07-17 00:05:59.400 | Loaded 0 plugins from git repository
2025-07-17 00:05:59.400 | Loading plugins from github repository...
2025-07-17 00:05:59.628 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-17 00:05:59.628 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-17 00:05:59.628 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-17 00:05:59.628 | Loaded 0 plugins from github repository
2025-07-17 00:05:59.628 | Plugin cache refreshed. Total plugins: 12
2025-07-17 00:05:59.628 | PluginRegistry initialized and cache populated.
2025-07-17 00:05:59.628 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-17 00:05:59.628 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-17 00:05:59.628 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-17 00:05:59.628 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-17 00:05:59.629 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-17 00:05:59.629 |   'ACCOMPLISH',
2025-07-17 00:05:59.629 |   'API_CLIENT',
2025-07-17 00:05:59.629 |   'CHAT',
2025-07-17 00:05:59.629 |   'RUN_CODE',
2025-07-17 00:05:59.629 |   'DATA_TOOLKIT',
2025-07-17 00:05:59.629 |   'FILE_OPERATION',
2025-07-17 00:05:59.629 |   'ASK_USER_QUESTION',
2025-07-17 00:05:59.629 |   'SCRAPE',
2025-07-17 00:05:59.629 |   'SEARCH',
2025-07-17 00:05:59.629 |   'TASK_MANAGER',
2025-07-17 00:05:59.629 |   'TEXT_ANALYSIS',
2025-07-17 00:05:59.629 |   'WEATHER'
2025-07-17 00:05:59.629 | ]
2025-07-17 00:05:59.629 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-17 00:05:59.629 |   'plugin-ACCOMPLISH',
2025-07-17 00:05:59.629 |   'plugin-API_CLIENT',
2025-07-17 00:05:59.629 |   'plugin-CHAT',
2025-07-17 00:05:59.629 |   'plugin-CODE_EXECUTOR',
2025-07-17 00:05:59.629 |   'plugin-DATA_TOOLKIT',
2025-07-17 00:05:59.629 |   'plugin-FILE_OPS_PYTHON',
2025-07-17 00:05:59.629 |   'plugin-ASK_USER_QUESTION',
2025-07-17 00:05:59.629 |   'plugin-SCRAPE',
2025-07-17 00:05:59.629 |   'plugin-SEARCH_PYTHON',
2025-07-17 00:05:59.629 |   'plugin-TASK_MANAGER',
2025-07-17 00:05:59.629 |   'plugin-TEXT_ANALYSIS',
2025-07-17 00:05:59.629 |   'plugin-WEATHER'
2025-07-17 00:05:59.629 | ]
2025-07-17 00:05:59.633 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-17 00:05:59.633 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-17 00:05:59.633 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-17 00:05:59.633 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-17 00:05:59.633 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-17 00:05:59.633 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-17 00:05:59.633 |     at async CapabilitiesManager.initialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:81:21)
2025-07-17 00:05:59.633 |     at async tryInitialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:57:17)
2025-07-17 00:05:59.633 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-17 00:05:59.633 | Loaded 0 plugins from github repository
2025-07-17 00:05:59.633 | Plugin cache refreshed. Total plugins: 12
2025-07-17 00:05:59.633 | PluginRegistry initialized and cache populated.
2025-07-17 00:05:59.633 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-17 00:05:59.633 |   'ACCOMPLISH',
2025-07-17 00:05:59.633 |   'API_CLIENT',
2025-07-17 00:05:59.633 |   'CHAT',
2025-07-17 00:05:59.633 |   'RUN_CODE',
2025-07-17 00:05:59.633 |   'DATA_TOOLKIT',
2025-07-17 00:05:59.633 |   'FILE_OPERATION',
2025-07-17 00:05:59.633 |   'ASK_USER_QUESTION',
2025-07-17 00:05:59.633 |   'SCRAPE',
2025-07-17 00:05:59.633 |   'SEARCH',
2025-07-17 00:05:59.633 |   'TASK_MANAGER',
2025-07-17 00:05:59.633 |   'TEXT_ANALYSIS',
2025-07-17 00:05:59.633 |   'WEATHER'
2025-07-17 00:05:59.633 | ]
2025-07-17 00:05:59.634 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-17 00:05:59.634 |   'plugin-ACCOMPLISH',
2025-07-17 00:05:59.634 |   'plugin-API_CLIENT',
2025-07-17 00:05:59.634 |   'plugin-CHAT',
2025-07-17 00:05:59.634 |   'plugin-CODE_EXECUTOR',
2025-07-17 00:05:59.634 |   'plugin-DATA_TOOLKIT',
2025-07-17 00:05:59.634 |   'plugin-FILE_OPS_PYTHON',
2025-07-17 00:05:59.634 |   'plugin-ASK_USER_QUESTION',
2025-07-17 00:05:59.634 |   'plugin-SCRAPE',
2025-07-17 00:05:59.634 |   'plugin-SEARCH_PYTHON',
2025-07-17 00:05:59.634 |   'plugin-TASK_MANAGER',
2025-07-17 00:05:59.634 |   'plugin-TEXT_ANALYSIS',
2025-07-17 00:05:59.634 |   'plugin-WEATHER'
2025-07-17 00:05:59.634 | ]
2025-07-17 00:05:59.634 | [CapabilitiesManager-constructor-02dcc038] CapabilitiesManager.initialize: PluginRegistry initialized.
2025-07-17 00:05:59.635 | [CapabilitiesManager-constructor-02dcc038] CapabilitiesManager.initialize: ConfigManager initialized.
2025-07-17 00:05:59.635 | [CapabilitiesManager-constructor-02dcc038] Setting up express server...
2025-07-17 00:05:59.647 | [CapabilitiesManager-constructor-02dcc038] CapabilitiesManager server listening on port 5060
2025-07-17 00:05:59.647 | [CapabilitiesManager-constructor-02dcc038] CapabilitiesManager server setup complete
2025-07-17 00:05:59.647 | [CapabilitiesManager-constructor-02dcc038] CapabilitiesManager.initialize: CapabilitiesManager initialization completed.
2025-07-17 00:14:48.581 | Created ServiceTokenManager for CapabilitiesManager
2025-07-17 00:14:48.607 | In executeAccomplishPlugin
2025-07-17 00:14:48.610 | LocalRepo: Loading fresh plugin list
2025-07-17 00:14:48.610 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-17 00:14:48.612 | LocalRepo: Loading from  [
2025-07-17 00:14:48.612 |   'ACCOMPLISH',
2025-07-17 00:14:48.612 |   'API_CLIENT',
2025-07-17 00:14:48.612 |   'CHAT',
2025-07-17 00:14:48.612 |   'CODE_EXECUTOR',
2025-07-17 00:14:48.612 |   'DATA_TOOLKIT',
2025-07-17 00:14:48.612 |   'FILE_OPS_PYTHON',
2025-07-17 00:14:48.612 |   'GET_USER_INPUT',
2025-07-17 00:14:48.612 |   'SCRAPE',
2025-07-17 00:14:48.613 |   'SEARCH_PYTHON',
2025-07-17 00:14:48.613 |   'TASK_MANAGER',
2025-07-17 00:14:48.613 |   'TEXT_ANALYSIS',
2025-07-17 00:14:48.613 |   'WEATHER'
2025-07-17 00:14:48.613 | ]
2025-07-17 00:14:48.613 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:14:48.614 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-17 00:14:48.615 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-17 00:14:48.617 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-17 00:14:48.621 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-17 00:14:48.622 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-17 00:14:48.625 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-17 00:14:48.627 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-17 00:14:48.628 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-17 00:14:48.629 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-17 00:14:48.630 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-17 00:14:48.632 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-17 00:14:48.636 | LocalRepo: Locators count 12
2025-07-17 00:14:48.640 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:14:48.644 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-17 00:14:48.644 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-17 00:14:48.644 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-17 00:14:48.646 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-17 00:14:48.646 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-17 00:14:48.648 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-17 00:14:48.649 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-17 00:14:48.652 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-17 00:14:48.660 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-17 00:14:48.661 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-17 00:14:48.662 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-17 00:14:49.405 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executeAccomplishPlugin: Plugins string for ACCOMPLISH: - DELEGATE: Create sub-agents with goals of their own.
2025-07-17 00:14:49.405 | - THINK: - sends prompts to the chat function...
2025-07-17 00:14:49.405 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-17 00:14:49.405 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-17 00:14:49.405 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-17 00:14:49.405 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-17 00:14:49.405 |     at async PluginMarketplace.getAvailablePluginsStr (/usr/src/app/marketplace/dist/PluginMarketplace.js:356:34)
2025-07-17 00:14:49.405 |     at async PluginRegistry.getAvailablePluginsStr (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:302:20)
2025-07-17 00:14:49.405 |     at async CapabilitiesManager.executeAccomplishPlugin (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:1241:35)
2025-07-17 00:14:49.405 |     at async CapabilitiesManager.executeActionVerb (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:394:47)
2025-07-17 00:14:49.405 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-17 00:14:49.406 | PluginRegistry.fetchOneByVerb called for verb: ACCOMPLISH
2025-07-17 00:14:49.407 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-17 00:14:49.407 | Using inline plugin path for plugin-ACCOMPLISH (ACCOMPLISH): /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-17 00:14:49.408 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executePlugin: Executing plugin plugin-ACCOMPLISH v1.0.0 (ACCOMPLISH) at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-17 00:14:49.451 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executePythonPlugin: Python execution - Main file path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py, Root path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-17 00:14:49.486 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Found python executable with command: python3 --version
2025-07-17 00:14:49.486 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Creating virtual environment at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv.
2025-07-17 00:14:49.486 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Running command: python3 -m venv "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv"
2025-07-17 00:14:58.008 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Upgrading pip with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install --upgrade pip
2025-07-17 00:15:02.755 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Installing requirements with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install -r "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt"
2025-07-17 00:15:05.438 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Python dependency installation stdout: Collecting requests>=2.28.0 (from -r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-17 00:15:05.438 |   Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
2025-07-17 00:15:05.438 | Collecting charset_normalizer<4,>=2 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-17 00:15:05.438 |   Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl.metadata (35 kB)
2025-07-17 00:15:05.438 | Collecting idna<4,>=2.5 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-17 00:15:05.438 |   Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
2025-07-17 00:15:05.438 | Collecting urllib3<3,>=1.21.1 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-17 00:15:05.438 |   Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
2025-07-17 00:15:05.438 | Collecting certifi>=2017.4.17 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-17 00:15:05.438 |   Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
2025-07-17 00:15:05.438 | Downloading requests-2.32.4-py3-none-any.whl (64 kB)
2025-07-17 00:15:05.438 | Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl (149 kB)
2025-07-17 00:15:05.438 | Downloading idna-3.10-py3-none-any.whl (70 kB)
2025-07-17 00:15:05.438 | Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)
2025-07-17 00:15:05.438 | Downloading certifi-2025.7.14-py3-none-any.whl (162 kB)
2025-07-17 00:15:05.438 | Installing collected packages: urllib3, idna, charset_normalizer, certifi, requests
2025-07-17 00:15:05.438 | 
2025-07-17 00:15:05.438 | Successfully installed certifi-2025.7.14 charset_normalizer-3.4.2 idna-3.10 requests-2.32.4 urllib3-2.5.0
2025-07-17 00:15:05.438 | 
2025-07-17 00:15:05.439 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.ensurePythonDependencies: Python dependencies processed successfully for /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH. Marker file updated.
2025-07-17 00:15:05.440 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executePythonPlugin: Executing Python command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/python" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH" < "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/.temp_input.json"
2025-07-17 00:15:05.440 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executePythonPlugin: Piping inputsJsonString to Python plugin: [["goal",{"inputName":"goal","value":"Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","valueType":"string","args":{}}],["verbToAvoid",{"inputName":"verbToAvoid","value":"EXECUTE","valueType":"string","args":{}}],["available_plugins",{"inputName":"available_plugins","value":"- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location","valueType":"string","args":{}}],["postOffice_url",{"inputName":"postOffice_url","value":"postoffice:5020","valueType":"string","args":{}}],["brain_url",{"inputName":"brain_url","value":"brain:5070","valueType":"string","args":{}}],["librarian_url",{"inputName":"librarian_url","value":"librarian:5040","valueType":"string","args":{}}],["__auth_token",{"inputName":"__auth_token","value":"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["__brain_auth_token",{"inputName":"__brain_auth_token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["token",{"inputName":"token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}]]
2025-07-17 00:15:06.119 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executePythonPlugin: Raw stdout from Python plugin ACCOMPLISH v1.0.0:
2025-07-17 00:15:06.119 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-17 04:15:05,672 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Using provided plugin context or fallback\n2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-17 04:15:05,997 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-17 04:15:05,997 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-17 04:15:05,997 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-17 04:15:06,091 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-17 00:15:06.119 | 
2025-07-17 00:15:06.119 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.executePythonPlugin: Raw stderr from Python plugin ACCOMPLISH v1.0.0:
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,672 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Using provided plugin context or fallback
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,997 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,997 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-17 00:15:06.119 | 2025-07-17 04:15:05,997 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-17 00:15:06.119 | 2025-07-17 04:15:06,091 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-17 00:15:06.119 | 
2025-07-17 00:15:06.120 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.validatePythonOutput: Validating Python output for ACCOMPLISH v1.0.0. Received stdout:
2025-07-17 00:15:06.120 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-17 04:15:05,672 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Using provided plugin context or fallback\n2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-17 04:15:05,672 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-17 04:15:05,997 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-17 04:15:05,997 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-17 04:15:05,997 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-17 04:15:06,091 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-17 00:15:06.120 | 
2025-07-17 00:15:06.120 | [1a0af3fb-3128-400e-b87d-d563bb35fd80] CapabilitiesManager.validatePythonOutput: Python plugin output parsed and validated successfully for ACCOMPLISH v1.0.0